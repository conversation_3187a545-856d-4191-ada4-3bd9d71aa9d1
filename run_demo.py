"""
MCP高并发演示启动脚本
自动启动本地测试服务器并运行演示
"""

import asyncio
import subprocess
import sys
import time
import httpx
from typing import Optional


class DemoRunner:
    """演示运行器"""
    
    def __init__(self):
        self.test_server_process: Optional[subprocess.Popen] = None
        
    async def start_test_server(self):
        """启动本地测试服务器"""
        print("🚀 启动本地测试服务器...")
        
        self.test_server_process = subprocess.Popen(
            [sys.executable, "local_test_server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待服务器启动
        await asyncio.sleep(3)
        
        # 检查服务器是否正常运行
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:9000/")
                if response.status_code == 200:
                    print("✅ 本地测试服务器启动成功 (http://localhost:9000)")
                    return True
        except Exception as e:
            print(f"❌ 本地测试服务器启动失败: {e}")
            return False
            
    def stop_test_server(self):
        """停止本地测试服务器"""
        if self.test_server_process:
            self.test_server_process.terminate()
            self.test_server_process.wait()
            print("🛑 本地测试服务器已停止")
            
    async def run_internal_concurrency_test(self):
        """运行内部并发测试"""
        print("\n" + "="*60)
        print("📊 内部并发处理测试")
        print("="*60)
        
        # 模拟内部并发测试的核心逻辑
        import httpx
        from asyncio import Semaphore
        
        # 配置
        MAX_CONCURRENT = 20
        semaphore = Semaphore(MAX_CONCURRENT)
        client = httpx.AsyncClient(
            limits=httpx.Limits(max_connections=50),
            timeout=10.0
        )
        
        stats = {"concurrent_requests": 0, "max_concurrent": 0}
        
        async def fetch_single_url(url: str) -> dict:
            async with semaphore:
                stats["concurrent_requests"] += 1
                stats["max_concurrent"] = max(stats["max_concurrent"], stats["concurrent_requests"])
                
                try:
                    response = await client.get(url)
                    response.raise_for_status()
                    return {"status": "success", "data": response.json()}
                except Exception as e:
                    return {"status": "error", "error": str(e)}
                finally:
                    stats["concurrent_requests"] -= 1
        
        # 测试URL
        test_urls = [
            "http://localhost:9000/json",
            "http://localhost:9000/uuid", 
            "http://localhost:9000/ip",
            "http://localhost:9000/user-agent",
            "http://localhost:9000/headers"
        ]
        
        try:
            # 串行测试
            print("1. 串行处理:")
            start_time = time.time()
            serial_results = []
            for i, url in enumerate(test_urls, 1):
                print(f"   处理请求 {i}...")
                result = await fetch_single_url(url)
                serial_results.append(result)
                if result["status"] == "success":
                    print(f"   ✅ 请求 {i} 成功")
                else:
                    print(f"   ❌ 请求 {i} 失败")
            
            serial_time = time.time() - start_time
            print(f"   串行总时间: {serial_time:.2f}秒")
            
            # 重置统计
            stats["max_concurrent"] = 0
            
            # 并发测试
            print("\n2. 并发处理:")
            start_time = time.time()
            print("   同时处理所有请求...")
            
            tasks = [fetch_single_url(url) for url in test_urls]
            concurrent_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            concurrent_time = time.time() - start_time
            
            successful = sum(1 for r in concurrent_results 
                           if isinstance(r, dict) and r.get("status") == "success")
            
            print(f"   成功: {successful}/{len(test_urls)}")
            print(f"   并发总时间: {concurrent_time:.2f}秒")
            
            # 性能对比
            print("\n3. 性能对比:")
            print(f"   串行处理: {serial_time:.2f}秒")
            print(f"   并发处理: {concurrent_time:.2f}秒")
            
            if serial_time > 0 and concurrent_time > 0:
                speedup = serial_time / concurrent_time
                improvement = (serial_time - concurrent_time) / serial_time * 100
                
                print(f"   加速倍数: {speedup:.1f}x")
                print(f"   性能提升: {improvement:.1f}%")
                
                if speedup > 1.5:
                    print("   ✅ 并发效果显著！")
                else:
                    print("   ⚠️  并发效果有限")
            
            print(f"\n4. 并发统计:")
            print(f"   最大并发数: {stats['max_concurrent']}")
            print(f"   配置的最大并发: {MAX_CONCURRENT}")
            
        finally:
            await client.aclose()
            
    async def run_comparison_test(self):
        """运行并发策略对比测试"""
        print("\n" + "="*60)
        print("🔄 运行完整的并发策略对比测试...")
        print("="*60)
        
        # 运行外部测试脚本
        process = subprocess.Popen(
            [sys.executable, "concurrency_comparison_test.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            print(stdout)
        else:
            print(f"❌ 对比测试失败:")
            print(stderr)
            
    async def show_summary(self):
        """显示总结"""
        print("\n" + "="*60)
        print("🎯 演示总结")
        print("="*60)
        
        print("✅ 完成的测试:")
        print("   • 本地测试服务器验证")
        print("   • 内部并发处理测试")
        print("   • 并发策略对比测试")
        
        print("\n💡 关键发现:")
        print("   • 本地服务器提供稳定的测试基准")
        print("   • 并发处理显著提升性能")
        print("   • 连接池和信号量是关键优化")
        print("   • 高并发控制平衡性能与稳定性")
        
        print("\n🚀 下一步:")
        print("   • 尝试启动SSE服务器: python sse_concurrent_server.py")
        print("   • 测试多客户端并发: python sse_client_test.py")
        print("   • 对比stdio vs SSE: python stdio_vs_sse_comparison.py")
        
    async def run_demo(self):
        """运行完整演示"""
        print("🌟 MCP高并发服务器演示")
        print("自动化测试本地服务器和并发处理能力")
        print("="*60)
        
        try:
            # 启动本地测试服务器
            if not await self.start_test_server():
                print("❌ 无法启动本地测试服务器，演示终止")
                return
                
            # 运行内部并发测试
            await self.run_internal_concurrency_test()
            
            # 运行对比测试
            await self.run_comparison_test()
            
            # 显示总结
            await self.show_summary()
            
        except KeyboardInterrupt:
            print("\n⏹️  演示被用户中断")
        except Exception as e:
            print(f"\n❌ 演示过程中出错: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理资源
            self.stop_test_server()


async def main():
    """主函数"""
    demo = DemoRunner()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
