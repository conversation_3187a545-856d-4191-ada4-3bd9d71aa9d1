"""
简化的高并发MCP服务器
演示核心并发设计概念
"""

import mcp
import httpx
import asyncio
from asyncio import Semaphore

# 创建MCP应用
app = mcp.server.FastMCP()

# 高并发配置
MAX_CONNECTIONS = 50        # 最大HTTP连接数
MAX_CONCURRENT = 20         # 最大并发请求数

# 创建高性能HTTP客户端
client = httpx.AsyncClient(
    limits=httpx.Limits(max_connections=MAX_CONNECTIONS),
    timeout=30.0
)

# 并发控制：限制同时处理的请求数
semaphore = Semaphore(MAX_CONCURRENT)

# 统计信息
stats = {
    "total_requests": 0,
    "concurrent_requests": 0,
    "max_concurrent": 0
}


# 内部核心函数 - 不是MCP工具
async def _fetch_single_url(url: str) -> dict:
    """
    内部URL获取函数 - 真正的并发处理逻辑
    """
    async with semaphore:
        stats["concurrent_requests"] += 1
        stats["total_requests"] += 1
        stats["max_concurrent"] = max(stats["max_concurrent"], stats["concurrent_requests"])

        try:
            response = await client.get(url)
            response.raise_for_status()

            return {
                "status": "success",
                "data": response.json(),
                "status_code": response.status_code
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
        finally:
            stats["concurrent_requests"] -= 1


@app.tool()
async def fetch_data(url: str) -> dict:
    """
    MCP工具：获取单个URL数据
    """
    return await _fetch_single_url(url)


@app.tool()
async def batch_fetch(urls: list[str]) -> dict:
    """
    MCP工具：批量并发获取数据
    """
    # 使用内部函数实现真正的并发
    tasks = [_fetch_single_url(url) for url in urls]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    successful = sum(1 for r in results if isinstance(r, dict) and r.get("status") == "success")

    return {
        "total": len(urls),
        "successful": successful,
        "failed": len(urls) - successful,
        "results": results
    }


@app.tool()
async def get_stats() -> dict:
    """
    获取服务器统计
    """
    return {
        "total_requests": stats["total_requests"],
        "current_concurrent": stats["concurrent_requests"],
        "max_concurrent_reached": stats["max_concurrent"],
        "config": {
            "max_connections": MAX_CONNECTIONS,
            "max_concurrent": MAX_CONCURRENT
        }
    }


if __name__ == "__main__":
    print("启动高并发MCP服务器")
    print(f"最大连接数: {MAX_CONNECTIONS}")
    print(f"最大并发数: {MAX_CONCURRENT}")
    print("注意: 虽然使用stdio，但内部工具调用支持并发处理")

    try:
        # FastMCP内部支持并发处理，即使使用stdio传输
        app.run(transport="stdio")
    except KeyboardInterrupt:
        print("\n服务器已停止")
    finally:
        asyncio.run(client.aclose())
