"""
本地测试HTTP服务器
提供稳定的测试端点，替代外部API
"""

import asyncio
import json
import time
import uuid
from datetime import datetime
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn


app = FastAPI(title="Local Test Server")

# 全局统计
stats = {
    "total_requests": 0,
    "start_time": time.time()
}


@app.get("/")
async def root():
    """根路径"""
    return {"message": "Local Test Server", "status": "running"}


@app.get("/json")
async def get_json():
    """返回JSON数据"""
    stats["total_requests"] += 1
    return {
        "slideshow": {
            "author": "Yours Truly",
            "date": "date of publication",
            "slides": [
                {
                    "title": "Wake up to WonderWidgets!",
                    "type": "all"
                },
                {
                    "items": [
                        "Why <em>WonderWidgets</em> are great",
                        "Who <em>buys</em> WonderWidgets"
                    ],
                    "title": "Overview",
                    "type": "all"
                }
            ],
            "title": "Sample Slide Show"
        },
        "request_id": stats["total_requests"],
        "timestamp": datetime.now().isoformat()
    }


@app.get("/uuid")
async def get_uuid():
    """生成UUID"""
    stats["total_requests"] += 1
    return {
        "uuid": str(uuid.uuid4()),
        "request_id": stats["total_requests"],
        "timestamp": datetime.now().isoformat()
    }


@app.get("/ip")
async def get_ip():
    """返回IP信息"""
    stats["total_requests"] += 1
    return {
        "origin": "127.0.0.1",
        "request_id": stats["total_requests"],
        "timestamp": datetime.now().isoformat()
    }


@app.get("/user-agent")
async def get_user_agent():
    """返回User-Agent信息"""
    stats["total_requests"] += 1
    return {
        "user-agent": "httpx/0.25.0",
        "request_id": stats["total_requests"],
        "timestamp": datetime.now().isoformat()
    }


@app.get("/headers")
async def get_headers():
    """返回请求头信息"""
    stats["total_requests"] += 1
    return {
        "headers": {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Host": "localhost:9000",
            "User-Agent": "httpx/0.25.0"
        },
        "request_id": stats["total_requests"],
        "timestamp": datetime.now().isoformat()
    }


@app.get("/delay/{seconds}")
async def delay_response(seconds: int):
    """延迟响应"""
    if seconds > 10:  # 限制最大延迟
        seconds = 10
        
    stats["total_requests"] += 1
    await asyncio.sleep(seconds)
    
    return {
        "args": {},
        "data": "",
        "files": {},
        "form": {},
        "headers": {
            "Accept": "*/*",
            "Host": "localhost:9000",
            "User-Agent": "httpx/0.25.0"
        },
        "origin": "127.0.0.1",
        "url": f"http://localhost:9000/delay/{seconds}",
        "delay": seconds,
        "request_id": stats["total_requests"],
        "timestamp": datetime.now().isoformat()
    }


@app.get("/stats")
async def get_stats():
    """获取服务器统计"""
    uptime = time.time() - stats["start_time"]
    return {
        "total_requests": stats["total_requests"],
        "uptime_seconds": round(uptime, 2),
        "requests_per_second": round(stats["total_requests"] / max(uptime, 1), 2),
        "start_time": datetime.fromtimestamp(stats["start_time"]).isoformat(),
        "current_time": datetime.now().isoformat()
    }


@app.get("/reset")
async def reset_stats():
    """重置统计"""
    stats["total_requests"] = 0
    stats["start_time"] = time.time()
    return {"message": "Stats reset", "timestamp": datetime.now().isoformat()}


if __name__ == "__main__":
    print("🚀 启动本地测试服务器")
    print("地址: http://localhost:9000")
    print("端点:")
    print("  /json      - JSON数据")
    print("  /uuid      - UUID生成")
    print("  /ip        - IP信息")
    print("  /delay/N   - N秒延迟响应")
    print("  /stats     - 服务器统计")
    print("使用 Ctrl+C 停止服务器")
    
    try:
        uvicorn.run(app, host="localhost", port=9000, log_level="warning")
    except KeyboardInterrupt:
        print("\n服务器已停止")
