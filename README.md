# MCP高并发服务器

简单演示MCP服务器的高并发处理设计。

## 文件说明

```
mcp_concurrent/
├── requirements.txt              # 项目依赖
├── simple_concurrent_server.py   # 高并发MCP服务器
├── test_internal_concurrency.py  # 内部并发测试
├── quick_test.py                 # 简单并发演示
└── README.md                     # 本文档
```

## 核心设计

### 高并发优化
- **SSE传输**: 支持真正的并发处理 (不是串行的stdio)
- **连接池**: httpx.Limits(max_connections=50)
- **并发控制**: asyncio.Semaphore(20) 限制同时处理的请求数
- **批量处理**: asyncio.gather() 并发执行多个请求

## 快速开始

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动服务器 (SSE传输，支持并发)
python simple_concurrent_server.py
# 服务器地址: http://localhost:8000

# 3. 测试并发效果 (新终端)
python test_concurrent_client.py

# 4. 简单并发演示
python quick_test.py
```

## 核心代码

### 1. HTTP客户端配置
```python
client = httpx.AsyncClient(
    limits=httpx.Limits(max_connections=50),  # 连接池
    timeout=30.0
)
```

### 2. 并发控制
```python
semaphore = Semaphore(20)  # 最多20个并发请求

async def fetch_data(url):
    async with semaphore:
        response = await client.get(url)
        return response.json()
```

### 3. 批量并发处理
```python
async def batch_fetch(urls):
    tasks = [fetch_data(url) for url in urls]
    results = await asyncio.gather(*tasks)
    return results
```

## 测试效果

运行 `python test_concurrent_client.py` 可以看到：

- **串行处理**: 3个2秒请求 = 6秒总时间
- **并发处理**: 3个2秒请求 = 2秒总时间
- **性能提升**: 3倍加速

## 关键区别

### ❌ stdio传输 (串行)
```python
app.run(transport="stdio")  # 一次只能处理一个请求
```

### ✅ SSE传输 (并发)
```python
app.run(transport="sse", host="localhost", port=8000)  # 支持并发处理
```

## 核心概念

1. **SSE传输**: 支持多个客户端同时连接和请求
2. **连接池**: 复用HTTP连接，减少建立连接开销
3. **信号量**: 控制同时处理的请求数量
4. **异步并发**: 网络I/O可以并行处理，不用等待

这就是MCP服务器高并发处理的核心设计。
